require("dotenv").config();
const Discord = require("discord.js");
const Jimp = require("jimp");
const config = require("./config.js");
// const Database = require("./database.js"); // معطل مؤقتاً
// const StatsManager = require("./stats.js"); // معطل مؤقتاً
// const StatsCommands = require("./commands/stats.js"); // معطل مؤقتاً
// const TicketManager = require("./tickets.js"); // معطل مؤقتاً
// const FollowUpManager = require("./followup.js"); // معطل مؤقتاً
// const cron = require('node-cron'); // معطل مؤقتاً
// const moment = require('moment'); // معطل مؤقتاً

// مصفوفة بسيطة لحفظ التقييمات مؤقتاً
let reviews = [];

// إنشاء عميل Discord
const client = new Discord.Client({
  intents: [
    Discord.IntentsBitField.Flags.Guilds,
    Discord.IntentsBitField.Flags.GuildMessages,
    Discord.IntentsBitField.Flags.MessageContent
  ],
});

// عندما يكون البوت جاهزًا
client.on("ready", () => {
  console.log(client.user.tag);
  console.log("Code By Wick Studio");
  console.log("discord.gg/wicks");
  client.user.setPresence({
    activities: [
      {
        name: `Feedback System v2.0`,
        type: Discord.ActivityType.Watching,
      },
    ],
  });

  // تهيئة الأنظمة بعد تشغيل البوت (معطل مؤقتاً)
  // ticketManager = new TicketManager(database);
  // followUpManager = new FollowUpManager(database, client);

  // جدولة التقارير التلقائية (معطل مؤقتاً)
  // setupAutomaticReports();

  console.log('✅ البوت جاهز للعمل!');
});

// إعداد التقارير التلقائية
function setupAutomaticReports() {
  // تقرير يومي في الساعة 9 صباحاً
  cron.schedule('0 9 * * *', async () => {
    try {
      const channel = client.channels.cache.get(config.Channel);
      if (channel) {
        const dailyReport = await statsManager.createDailyReport();
        await channel.send({
          content: '📋 **التقرير اليومي**',
          embeds: [dailyReport]
        });
      }
    } catch (error) {
      console.error('خطأ في إرسال التقرير اليومي:', error);
    }
  });

  // تقرير أسبوعي يوم الأحد في الساعة 10 صباحاً
  cron.schedule('0 10 * * 0', async () => {
    try {
      const channel = client.channels.cache.get(config.Channel);
      if (channel) {
        const weeklyReport = await statsManager.createPeriodStatsEmbed(7);
        await channel.send({
          content: '📊 **التقرير الأسبوعي**',
          embeds: [weeklyReport]
        });
      }
    } catch (error) {
      console.error('خطأ في إرسال التقرير الأسبوعي:', error);
    }
  });

  // تحديث الإحصائيات كل ساعة
  cron.schedule('0 * * * *', async () => {
    try {
      await database.updateDailyStats();
    } catch (error) {
      console.error('خطأ في تحديث الإحصائيات:', error);
    }
  });

  // معالجة المتابعات المستحقة كل 6 ساعات
  cron.schedule('0 */6 * * *', async () => {
    try {
      await followUpManager.processPendingFollowUps(config.Channel);
    } catch (error) {
      console.error('خطأ في معالجة المتابعات:', error);
    }
  });

  // تنظيف البيانات القديمة كل أسبوع (يوم الأحد 2 صباحاً)
  cron.schedule('0 2 * * 0', async () => {
    try {
      // حذف المتابعات المكتملة الأقدم من شهر
      const oneMonthAgo = moment().subtract(1, 'month').toISOString();
      const cleanupQuery = `DELETE FROM follow_ups WHERE completed = TRUE AND created_at < ?`;

      database.db.run(cleanupQuery, [oneMonthAgo], function(err) {
        if (err) {
          console.error('خطأ في تنظيف البيانات:', err);
        } else {
          console.log(`✅ تم حذف ${this.changes} متابعة قديمة`);
        }
      });
    } catch (error) {
      console.error('خطأ في تنظيف البيانات:', error);
    }
  });

  console.log('✅ تم إعداد التقارير التلقائية والمتابعات');
}

// عند تلقي رسالة
client.on("messageCreate", async (message) => {
  // تجاهل رسائل البوتات
  if (message.author.bot) return;

  // معالجة أوامر الإحصائيات (معطل مؤقتاً)
  if (message.content.startsWith(config.Prefix + "stats")) {
    if (!message.member.permissions.has(Discord.PermissionsBitField.Flags.Administrator)) {
      return message.reply('❌ هذا الأمر متاح للمديرين فقط');
    }

    // إحصائيات بسيطة من المصفوفة
    const totalReviews = reviews.length;
    const ratings = reviews.map(r => r.rating);
    const avgRating = ratings.length > 0 ? (ratings.reduce((a, b) => a + b, 0) / ratings.length).toFixed(2) : 0;

    const statsEmbed = new Discord.EmbedBuilder()
      .setTitle('📊 إحصائيات التقييمات')
      .setColor('#0099ff')
      .addFields(
        { name: '📈 إجمالي التقييمات', value: `${totalReviews}`, inline: true },
        { name: '⭐ متوسط التقييم', value: `${avgRating}/5.00`, inline: true },
        { name: '🕒 آخر تقييم', value: totalReviews > 0 ? reviews[reviews.length - 1].username : 'لا يوجد', inline: true }
      )
      .setTimestamp();

    await message.reply({ embeds: [statsEmbed] });
    return;
  }

  if (message.content === config.Prefix + "send") {
     if (!message.member.permissions.has(Discord.PermissionsBitField.Flags.Administrator)) return;

    // إعداد أزرار التقييم
    let row = new Discord.ActionRowBuilder().addComponents(
      new Discord.ButtonBuilder().setCustomId("star1").setLabel("⭐").setStyle(Discord.ButtonStyle.Secondary),
      new Discord.ButtonBuilder()
        .setCustomId("star2")
        .setLabel("⭐⭐")
        .setStyle(Discord.ButtonStyle.Secondary),
      new Discord.ButtonBuilder().setCustomId("star3").setLabel("⭐⭐⭐").setStyle(Discord.ButtonStyle.Secondary),
      new Discord.ButtonBuilder().setCustomId("star4").setLabel("⭐⭐⭐⭐").setStyle(Discord.ButtonStyle.Secondary),
      new Discord.ButtonBuilder().setCustomId("star5").setLabel("⭐⭐⭐⭐⭐").setStyle(Discord.ButtonStyle.Secondary)
    );
    // إعداد الرسالة الترويجية
    let embed = new Discord.EmbedBuilder()
    .setColor("#0a1a28") // يحدد لون الإطار الجانبي للرسالة
    .setTitle("أكتب_عنوان") // يضع عنوان للرسالة
    .setDescription("أكتب_وصف") // يضيف وصفًا تحت العنوان
    // .setImage("رابط_صورة") // يعرض صورة كبيرة (بنر) داخل الرسالة - معطل لأنه مش رابط صحيح
    .setThumbnail(message.guild.iconURL({ dynamic: true })); // يعرض صورة مصغرة، وهي شعار الخادم (server)
  message.channel.send({ embeds: [embed], components: [row] });
}
});
// عند التفاعل مع الأزرار

client.on("interactionCreate", async (interaction) => {
  // معالجة تفاعلات التذاكر (معطل مؤقتاً)
  // if (interaction.customId.includes('ticket') ||
  //     interaction.customId.includes('refresh') ||
  //     interaction.customId.includes('close') ||
  //     interaction.customId.includes('assign') ||
  //     interaction.customId.includes('progress')) {
  //   await ticketManager.handleTicketInteraction(interaction);
  //   return;
  // }

  // معالجة تفاعلات المتابعة (معطل مؤقتاً)
  // if (interaction.customId.includes('contact_user') ||
  //     interaction.customId.includes('request_new_review') ||
  //     interaction.customId.includes('satisfaction') ||
  //     interaction.customId.includes('remind_later') ||
  //     interaction.customId.includes('start_new_review')) {
  //   await followUpManager.handleFollowUpInteraction(interaction);
  //   return;
  // }

  if (interaction.customId.startsWith("star")) {
    let starId = interaction.customId.slice(-1);
    let modal = new Discord.ModalBuilder()
      .setCustomId(`thstar${starId}`)
      .setTitle(`تقييم`);
    let textInput = new Discord.TextInputBuilder()
      .setCustomId(`fedd${starId}`)
      .setLabel(`تقييمك`)
      .setStyle(Discord.TextInputStyle.Paragraph)
      .setMinLength(1)
      .setMaxLength(100)
      .setPlaceholder(`يرجى وضع رأيك هنا`)
      .setRequired(true);
    const row = new Discord.ActionRowBuilder().addComponents(textInput);
    modal.addComponents(row);
    interaction.showModal(modal);
  }
});

// عند إرسال النموذج
client.on("interactionCreate", async (modal) => {
  if (modal.customId.startsWith("thstar")) {
    let starId = modal.customId.slice(-1);
    let msg = modal.fields.getTextInputValue(`fedd${starId}`);

    // إنشاء صورة التقييم باستخدام Jimp
    try {
      // تحميل صورة الخلفية
      const backgroundImage = await Jimp.read(`./Images/star${starId}.png`);

      // تحميل صورة المستخدم
      const userAvatarUrl = modal.user.avatarURL({ extension: "png", size: 256 });
      const userAvatar = await Jimp.read(userAvatarUrl);

      // تغيير حجم الأفتار وجعله دائري
      userAvatar.resize(260, 260);
      userAvatar.circle();

      // إضافة الأفتار للصورة الأساسية
      backgroundImage.composite(userAvatar, 58, 47);

      // إضافة النص (اسم المستخدم)
      const font = await Jimp.loadFont(Jimp.FONT_SANS_32_WHITE);
      backgroundImage.print(font, 407, 51, `بواسطة: ${modal.user.displayName}`);

      // إضافة نص التقييم (مقسم لأسطر)
      const charactersPerLine = 25;
      const lines = [];
      for (let i = 0; i < msg.length; i += charactersPerLine) {
        lines.push(msg.slice(i, i + charactersPerLine));
      }

      lines.forEach((line, index) => {
        const yPos = 95 + (index * 35);
        backgroundImage.print(font, 406, yPos, line);
      });

      // تحويل الصورة إلى Buffer
      const imageBuffer = await backgroundImage.getBufferAsync(Jimp.MIME_PNG);
      var attachment = new Discord.AttachmentBuilder(imageBuffer, { name: `Star${starId}.png` });

    } catch (imageError) {
      console.error('خطأ في إنشاء الصورة:', imageError);
      // في حالة فشل الصورة، نرسل embed بسيط
      var attachment = null;
    }
    let channel = client.channels.cache.get(config.Channel);

    try {
      // حفظ التقييم في المصفوفة مؤقتاً
      const review = {
        id: reviews.length + 1,
        userId: modal.user.id,
        username: modal.user.displayName,
        guildId: modal.guild.id,
        rating: parseInt(starId),
        message: msg,
        timestamp: new Date(),
        imagePath: `Star${starId}.png`
      };
      reviews.push(review);
      console.log(`✅ تم حفظ التقييم - ID: ${review.id}`);

      // إشعار للتقييمات السيئة (1-2 نجوم)
      if (parseInt(starId) <= 2) {
        const adminEmbed = new Discord.EmbedBuilder()
          .setTitle('⚠️ تقييم سيء يحتاج انتباه')
          .setColor('#ff6b6b')
          .addFields(
            { name: 'المستخدم', value: modal.user.displayName, inline: true },
            { name: 'التقييم', value: '⭐'.repeat(parseInt(starId)), inline: true },
            { name: 'الرسالة', value: msg, inline: false },
            { name: '📊 إجمالي التقييمات', value: `${reviews.length}`, inline: true }
          )
          .setTimestamp();

        await channel.send({ embeds: [adminEmbed] });
      }

      await modal.reply({ content: "شكرًا على ملاحظاتك", ephemeral: true }).then(async () => {
        if (attachment) {
          await channel.send({ files: [attachment] });
        } else {
          // إرسال embed بسيط في حالة فشل الصورة
          const reviewEmbed = new Discord.EmbedBuilder()
            .setTitle(`تقييم جديد - ${'⭐'.repeat(parseInt(starId))}`)
            .setColor(parseInt(starId) <= 2 ? '#ff6b6b' : parseInt(starId) === 3 ? '#ffa500' : '#4caf50')
            .addFields(
              { name: '👤 المستخدم', value: modal.user.displayName, inline: true },
              { name: '⭐ التقييم', value: '⭐'.repeat(parseInt(starId)), inline: true },
              { name: '📝 الرسالة', value: msg, inline: false }
            )
            .setThumbnail(modal.user.avatarURL({ dynamic: true }))
            .setTimestamp();
          await channel.send({ files: [attachment] });
        }
      });

    } catch (error) {
      console.error('خطأ في حفظ التقييم:', error);
      await modal.reply({ content: "حدث خطأ في حفظ تقييمك، يرجى المحاولة مرة أخرى", ephemeral: true });
    }
  }
});

client.login(config.Token);
