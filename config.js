module.exports = {
    Token: process.env.TOKEN || "MTM4MDU0NjYxMzczNDUzOTQyNA.GL7r5T.T7bM-JCuRBhL9eaHd3Bg4WvSBMyo69sX5CRSVA",  // token bot من متغيرات البيئة
    Channel: "1336009286396153907", // الأيدي حق روم لي يرسل فيها بوت تقييم
    Prefix: "!", // بداية أمر  مثال !send

    // إعدادات قاعدة البيانات
    DatabasePath: process.env.DATABASE_PATH || "./feedback.db",

    // إعدادات التقارير
    Reports: {
        Daily: "0 9 * * *",      // تقرير يومي 9 صباحاً
        Weekly: "0 10 * * 0",    // تقرير أسبوعي الأحد 10 صباحاً
        StatsUpdate: "0 * * * *", // تحديث الإحصائيات كل ساعة
        FollowUp: "0 */6 * * *"   // معالجة المتابعات كل 6 ساعات
    },

    // إعدادات المتابعة
    FollowUpSettings: {
        BadReviewDays: 3,    // متابعة التقييمات السيئة بعد 3 أيام
        TicketClosedDays: 7, // متابعة التذاكر المغلقة بعد 7 أيام
        ReminderDays: 3      // تذكير بعد 3 أيام
    },

    // إعدادات التذاكر
    TicketSettings: {
        AutoCreateForBadReviews: true, // إنشاء تذاكر تلقائية للتقييمات السيئة
        BadReviewThreshold: 2          // التقييمات 1-2 نجوم تعتبر سيئة
    }
}
