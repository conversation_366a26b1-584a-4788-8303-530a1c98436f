# Feedback Discord Bot

This is a simple Discord bot that allows users to send feedback in a specific channel using interactive buttons. The bot is built using `discord.js` and helps collect feedback efficiently with an easy-to-use interface.

## Features

- **Send Feedback Form**: Users can easily send their feedback using buttons.
- **Customizable Embed**: The bot allows customizing the feedback message with a title, description, and images.
- **Channel Configuration**: Feedback is sent to a designated channel defined in the `Data.js` configuration file.
- **Interactive Components**: The bot uses buttons for a seamless user experience.

## Requirements

- Node.js v18
- Discord.js v14.x
- Canvas v2.x

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/wickmrakchi/discord-bot-feedback.git
   cd discord-bot-feedback
